// class CustomLayer {
//     private zIndex:number=0;
//     private instances:any;
//     constructor() {
//         this.zIndex = 19891014; // 初始 zIndex
//         this.instances = {}; // 存储所有弹层实例
//     }
//
//     createOverlay() {
//         const overlay = document.createElement('div') as HTMLDivElement;
//         overlay.style.position = 'fixed';
//         overlay.style.top = String(0);
//         overlay.style.left = String(0);
//         overlay.style.width = '100%';
//         overlay.style.height = '100%';
//         overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
//         overlay.style.zIndex = String(this.zIndex++);
//         document.body.appendChild(overlay);
//         return overlay;
//     }
//
//     createLayer(options:any = {}) {
//         const layerDiv = document.createElement('div');
//         layerDiv.style.position = 'fixed';
//         layerDiv.style.zIndex = String(this.zIndex++);
//         layerDiv.style.backgroundColor = options.skin || 'white';
//         layerDiv.style.padding = '20px';
//         layerDiv.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
//         layerDiv.style.borderRadius = '5px';
//         layerDiv.style.maxWidth = options.maxWidth ? `${options.maxWidth}px` : 'auto';
//         layerDiv.style.maxHeight = options.maxHeight ? `${options.maxHeight}px` : 'auto';
//
//         // 设置宽高
//         if (Array.isArray(options.area)) {
//             layerDiv.style.width = options.area[0];
//             layerDiv.style.height = options.area[1];
//         } else if (typeof options.area === 'string') {
//             layerDiv.style.width = options.area;
//             layerDiv.style.height = 'auto';
//         } else {
//             layerDiv.style.width = 'auto';
//             layerDiv.style.height = 'auto';
//         }
//
//         // 设置偏移
//         if (Array.isArray(options.offset)) {
//             layerDiv.style.top = options.offset[0];
//             layerDiv.style.left = options.offset[1];
//         } else if (typeof options.offset === 'string') {
//             // 处理特殊偏移值 't', 'r', 'b', 'l', 'lt', 'lb', 'rt', 'rb'
//             switch (options.offset) {
//                 case 't': layerDiv.style.top = '0'; layerDiv.style.left = '50%'; layerDiv.style.transform = 'translateX(-50%)'; break;
//                 case 'r': layerDiv.style.top = '50%'; layerDiv.style.right = '0'; layerDiv.style.transform = 'translateY(-50%)'; break;
//                 case 'b': layerDiv.style.bottom = '0'; layerDiv.style.left = '50%'; layerDiv.style.transform = 'translateX(-50%)'; break;
//                 case 'l': layerDiv.style.top = '50%'; layerDiv.style.left = '0'; layerDiv.style.transform = 'translateY(-50%)'; break;
//                 case 'lt': layerDiv.style.top = '0'; layerDiv.style.left = '0'; break;
//                 case 'lb': layerDiv.style.bottom = '0'; layerDiv.style.left = '0'; break;
//                 case 'rt': layerDiv.style.top = '0'; layerDiv.style.right = '0'; break;
//                 case 'rb': layerDiv.style.bottom = '0'; layerDiv.style.right = '0'; break;
//                 default: layerDiv.style.top = '50%'; layerDiv.style.left = '50%'; layerDiv.style.transform = 'translate(-50%, -50%)'; break;
//             }
//         } else {
//             layerDiv.style.top = '50%';
//             layerDiv.style.left = '50%';
//             layerDiv.style.transform = 'translate(-50%, -50%)';
//         }
//
//         // 设置标题
//         if (options.title) {
//             const titleDiv = document.createElement('div');
//             titleDiv.style.marginBottom = '10px';
//             titleDiv.style.fontWeight = 'bold';
//             if (typeof options.title === 'string') {
//                 titleDiv.innerText = options.title;
//             } else if (Array.isArray(options.title)) {
//                 titleDiv.innerText = options.title[0];
//                 titleDiv.style.cssText = options.title[1];
//             }
//             layerDiv.appendChild(titleDiv);
//
//             if (options.maxmin) {
//                 const maxminBtn = document.createElement('button');
//                 maxminBtn.innerText = '[]';
//                 maxminBtn.style.position = 'absolute';
//                 maxminBtn.style.right = '30px';
//                 maxminBtn.style.top = '10px';
//                 maxminBtn.addEventListener('click', () => {
//                     if (layerDiv.style.width === '100%') {
//                         layerDiv.style.width = options.area ? options.area[0] : '500px';
//                         layerDiv.style.height = options.area ? options.area[1] : '300px';
//                         layerDiv.style.top = '50%';
//                         layerDiv.style.left = '50%';
//                         layerDiv.style.transform = 'translate(-50%, -50%)';
//                     } else {
//                         layerDiv.style.width = '100%';
//                         layerDiv.style.height = '100%';
//                         layerDiv.style.top = '0';
//                         layerDiv.style.left = '0';
//                         layerDiv.style.transform = 'none';
//                     }
//                 });
//                 layerDiv.appendChild(maxminBtn);
//             }
//
//             if (options.closeBtn !== 0) {
//                 const closeBtn = document.createElement('button');
//                 closeBtn.innerText = 'X';
//                 closeBtn.style.position = 'absolute';
//                 closeBtn.style.right = '10px';
//                 closeBtn.style.top = '10px';
//                 closeBtn.addEventListener('click', () => {
//                     document.body.removeChild(layerDiv);
//                     document.body.removeChild(overlay);
//                 });
//                 layerDiv.appendChild(closeBtn);
//             }
//         }
//
//         // 设置内容
//         if (options.content) {
//             const contentDiv = document.createElement('div');
//             if (typeof options.content === 'string') {
//                 contentDiv.innerHTML = options.content;
//             } else if (options.content instanceof HTMLElement) {
//                 contentDiv.appendChild(options.content);
//             }
//             layerDiv.appendChild(contentDiv);
//         }
//
//         document.body.appendChild(layerDiv);
//
//         return layerDiv;
//     }
//
//     open(options:any) {
//         const overlay = options.shade !== 0 ? this.createOverlay() : null;
//         const layerDiv = this.createLayer(options);
//         const instanceId = options.id || `layer${Date.now()}`;
//         this.instances[instanceId] = { layerDiv, overlay };
//
//         if (options.time > 0) {
//             setTimeout(() => {
//                 this.close(instanceId);
//             }, options.time);
//         }
//
//         if (typeof options.success === 'function') {
//             options.success(layerDiv, instanceId, this);
//         }
//
//         return instanceId;
//     }
//
//     close(id) {
//         const instance = this.instances[id];
//         if (instance) {
//             if (instance.layerDiv) document.body.removeChild(instance.layerDiv);
//             if (instance.overlay) document.body.removeChild(instance.overlay);
//             delete this.instances[id];
//         }
//     }
// }
