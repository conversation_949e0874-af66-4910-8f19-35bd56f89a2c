import { FinancialData ,LeaseDetail } from '../types/financial';
import { AlpineManager  } from './AlpineManager';
import { BaseComponent } from './BaseComponent';

export class FinancialSystemCom extends BaseComponent<FinancialData> {
    constructor(data: FinancialData | null) {
        super(data || FinancialSystemCom.getDefaultFinancialData());
        this.isVisible = true; 
        this.registerComponent();
    }

    /**
     * 获取默认的 FinancialData 数据
     */
    private static getDefaultFinancialData(): FinancialData {
        return {
            finance: {
                buCode: '',
                buName: '',
                finMarket: '',
                contractType: '',
                strategicAlliance: '',
                bizType: '',
                partyA: '',
                addOfPartyA: '',
                partyB: '',
                addOfPartyB: '',
                addOfStore: '',
                signDate: '',
                openDate: '',
                estimateOpen: '',
                returnAreaYes: '',
                noSublet: '',
                areaSM: '',
                areaRemark: '',
                certEffectiveDate: '',
                certExpiryDate: '',
                leaseTypePlanning: '',
                leaseTypeDE: '',
                leaseTypeRE: '',
                depositAmount: '',
                idcAmount: '',
                leaseIncentives: '',
                transferSubject: '',
                reNotes: '',
                bondAmount: '',
                bondReturnDate: '',
                guaranteeAmount: '',
                leaseDetails: []
            },
        };
    }

    /**
     * 注册组件
     */
    private registerComponent() {
        AlpineManager.registerComponent("financialSystemComponent", () => ({
            data: this.data,
            isVisible: this.isVisible,
            init: () => this.init(),
            validateForm: () => this.validateForm(),
            addLeases: () => this.addLeases(),
            scrollToSection: (targetId: string) => {
                const anchor = this.$el.querySelector(`[data-target="${targetId}"]`);
                if (anchor instanceof HTMLElement) {
                    this.scrollToTarget(anchor);
                }
            },
            toggleContent(event: Event) {
                event.stopPropagation();
                const header = event.currentTarget as HTMLElement;
                const section = header.closest('.po-child');
                if (section) {
                    section.classList.toggle('open');
                    const link = header.querySelector('.put-right');
                    if (link) {
                        link.textContent = section.classList.contains('open') ? '收起' : '展开';
                    }
                }
            },
            showDetails: (detail:LeaseDetail) => {
                // 实现显示详情逻辑
                console.log('Show details for:', detail);
            },
            deleteLease: (detail:LeaseDetail) => {
                // 实现删除逻辑
                this.data.finance.leaseDetails = this.data.finance.leaseDetails.filter(d => d !== detail);
            },
            getUseOfLeaseNoText: (useOfLeaseNo: string) => {
                const select = document.getElementById('useOfLeaseNoMapping') as HTMLSelectElement;
                const option = Array.from(select.options).find(opt => opt.value === useOfLeaseNo);
                return option ? useOfLeaseNo + ':' + option.text : '未知';
            }
        }));
    }

    private addLeases(): void {
        // 实现添加 leases 逻辑
    }
}

