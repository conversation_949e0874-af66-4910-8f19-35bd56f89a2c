import { LeaseDetail } from '../types/financial';
import { BaseComponent } from "./BaseComponent";
import { AlpineManager } from './AlpineManager';

/**
 * LeasesComponent 类用于管理租赁详情的组件
 * 继承自 BaseComponent，使用 Alpine.js 进行数据绑定和交互
 */
export class LeasesComponent extends BaseComponent<LeaseDetail> {

    /**
     * 构造函数
     * @param data - 初始化的 LeaseDetail 数据，如果为 null，则使用默认数据
     */
    constructor(data: LeaseDetail | null) {
        super(data || LeasesComponent.getDefaultLeaseDetail());
        this.isVisible = false;  // 默认设置为不可见
        this.registerComponent();
    }

    /**
     * 获取默认的 LeaseDetail 数据
     * @returns 默认的 LeaseDetail 对象
     */
    private static getDefaultLeaseDetail(): LeaseDetail {
        return {
            objectId: '',
            lease: {
                leaseCode: '',
                useOfLeaseNo: '',
                rentName: '',
                rentType: '',
                landlordCode: '',
                landlordName: '',
                receivedCode: '',
                receivedName: '',
                taxplayerJde: '',
                taxplayerName: '',
                mergeOthersYes: '',
                leaseGroup: '',
                strategyAlianceYes: '',
                rentAreaYes: '',
                yearlyNote: '',
                receiverNote: '',
                monthlyNote: '',
                unit: '',
                beginDate: '',
                freeBeginDate: '',
                freeEndDate: '',
                startRentDate: '',
                orgEndDate: '',
                salesCalculation: '',
                relation: ''
            },
            rb: {
                rbPaymentFrequency: '',
                rbPaymentType: '',
                paymentRent: '',
                dba: '',
                paymentEarlyRentYesDay: '',
                paymentLease: '',
                rbFeeDesc: '',
                rbAnnualSettleYes: '',
                rentAnnual: '',
                rbtrRent: '',
                rbtrMgmt: '',
                rbtrAdv: '',
                rbtrFee: '',
                relation: '',
                phases: []
            },
            sov: {
                sovBreakMonthYes: '',
                sovLeaseVAT: '',
                calculationMethod: '',
                sovPaymentType: '',
                minMaxNeedYes: '',
                minMaxType1: '',
                minMaxType2: '',
                sovRules: []
            }
        };
    }

    /**
     * 注册 Alpine.js 组件
     * 绑定数据和方法到组件中
     */
    private registerComponent() {
        AlpineManager.registerComponent("leasesComponent", () => ({
            data: this.data,
            isVisible: this.isVisible,
            validateForm() {
                return this.validateForm();  // 调用父类的 validateForm 方法
            },
            updateDataAndShow(newData: LeaseDetail) {
                this.updateDataAndShow(newData);
            },
            scrollToSection: (targetId: string) => {
                const anchor = this.$el.querySelector(`[data-target="${targetId}"]`);
                if (anchor instanceof HTMLElement) {
                    this.scrollToTarget(anchor);
                }
            },
        }));
    }

    /**
     * 更新数据并显示组件
     * @param newData - 新的 LeaseDetail 数据
     */
    public updateDataAndShow(newData: LeaseDetail) {
        if (newData !== null) {
            this.data = newData;
        }
        this.isVisible = true;  // 设置组件为可见
    }
}
