// 创建一个类型别名
import {DOMUtils} from "./DOMUtils.ts";

type FormElement = HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | HTMLButtonElement;

interface ComponentData<T> {
    [key: string]: T;
}

export abstract class BaseComponent<T = any>  {
    // 组件的 DOM 元素
    public $el!: HTMLElement;
    public data: T;
    public isVisible: boolean;

    constructor(data?: T) {
        this.data = data ?? {} as T;
        this.isVisible = false;
    }

    // 初始化表单元素，绑定 onchange 事件 在 html 里 写 x-init="init()"
    init() {
        // 如果 $el 未定义，则查找最近的 x-data 元素
        if (!this.$el) {
            const element = document.querySelector('[x-data]');
            if (element instanceof HTMLElement) {
                this.$el = element;
            }
            this.bindFormEvents();
            this.bindAnchorEvents();
        }
    }

    /**
     * 绑定表单元素的 change 事件
     * 当表单元素的值发生变化时，调用 onInputChange 方法进行验证
     */
    private bindFormEvents() {
        const formElements = this.$el.querySelectorAll('input, select, textarea');
        formElements.forEach((el) => {
            if (el instanceof HTMLInputElement || el instanceof HTMLSelectElement || el instanceof HTMLTextAreaElement) {
                el.removeEventListener('change', this.onInputChange);
                el.addEventListener('change', this.onInputChange.bind(this));
            }
        });
    }

    /**
     * 处理表单输入的 change 事件
     * 当表单元素的值发生变化时，调用 validateField 方法进行验证
     * @param event - 事件对象
     */
    private onInputChange(event: Event) {
        const el = event.target as FormElement;
        this.validateField(el);
    }

    /**
     * 验证字段
     * 该方法会检查表单元素是否满足必填和正则表达式的要求
     * @param el - 要验证的表单元素
     * @returns 布尔值，表示字段是否通过验证
     */
    private validateField(el: FormElement): boolean {
        let isValid = true;
        const container = el.parentElement as HTMLElement;
        if (!container) {
            return true;
        }

        // 1. 验证 `required` 属性
        if (el.hasAttribute('required')) {
            if (!this.isRequired(el)) {
                this.addErrorStyles(container);
                console.log(container);
                isValid = false;
            }
        }

        // 2. 验证 `pattern` 属性 
        if (el.hasAttribute('pattern')) {
            if (!this.hasPattern(el)) {
                this.addErrorStyles(container);
                isValid = false;
            }
        }

        // 如果所有验证都通过，移除错误样式
        if (isValid) {
            this.removeErrorStyles(container);
        }

        // 你可以在这里添加更多的验证规则（例如：min、max）
        return isValid;
    }

    /**
     * 验证表单
     * 该方法会遍历表单中的所有元素，调用 validateField 方法进行验证
     * 如果有元素未通过验证，会将其标记为无效，并滚动到第一个无效元素的位置
     * @returns 布尔值，表示表单是否通过验证
     */
    public validateForm(): boolean {
        let isValid = true;
        const sections = this.$el.querySelectorAll('.po-child');
        let firstInvalidElement: HTMLElement | null = null;

        // 清除所有导航项的错误样式
        const navItems = this.$el.querySelectorAll('.aimingPoint ul li');
        navItems.forEach(item => item.classList.remove('error'));

        sections.forEach(section => {
            const sectionId = section.id;
            const formElements = section.querySelectorAll('input, select, textarea');
            let sectionValid = true;

            formElements.forEach(element => {
                if (element instanceof HTMLInputElement || 
                    element instanceof HTMLSelectElement || 
                    element instanceof HTMLTextAreaElement) {
                    if (!this.validateField(element)) {
                        if (!firstInvalidElement) {
                            firstInvalidElement = element;
                        }
                        sectionValid = false;
                        isValid = false;

                        // 展开当前元素所在的 po-child
                        section.classList.add('open');
                        const headerLink = section.querySelector('.po-child-header .put-right');
                        if (headerLink) {
                            headerLink.textContent = '收起';
                        }
                    }
                }
            });

            // 如果该区域有验证失败的表单项，给对应的导航项添加警告图标
            if (!sectionValid) {
                this.addWarningIcon(sectionId);
            }
        });

        // 滚动到第一个错误元素
        if (firstInvalidElement) {
            DOMUtils.scrollToElement(firstInvalidElement);
        }

        return isValid;
    }

    /**
     * 为指定区域的导航项添加警告图标
     * @param sectionId - 区域ID
     */
    private addWarningIcon(sectionId: string): void {
        const navItem = this.$el.querySelector(`.aimingPoint ul li[data-target="${sectionId}"]`);
        if (navItem) {
            navItem.classList.add('error');
        }
    }

    /**
     * 检查表单元素是否为必填项
     * 如果元素的值为空字符串（去除首尾空格后），则返回 false
     * @param el - 要检查的表单元素
     * @returns 布尔值，表示字段是否为必填项
     */
    protected isRequired(el: FormElement): boolean {
        if (el instanceof HTMLSelectElement) {
            return el.value !== '';
        }
        if (el instanceof HTMLInputElement) {
            if (el.type === 'checkbox' || el.type === 'radio') {
                return el.checked;
            }
            return el.value.trim() !== '';
        }
        if (el instanceof HTMLTextAreaElement) {
            return el.value.trim() !== '';
        }
        return true;
    }

    /**
     * 检查表单元素是否具有 pattern 属性，并且该属性值是否与元素的值匹配
     * 如果元素具有 pattern 属性并且其值与正则表达式不匹配，则返回 false
     * @param el - 要检查的表单元素
     * @returns 布尔值，表示字段的 pattern 属性是否通过验证
     */
    private hasPattern(el: FormElement): boolean {
        if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {
            const pattern = el.getAttribute('pattern');
            if (pattern) {
                const regex = new RegExp(pattern);
                return regex.test(el.value);
            }
        }
        return true; // 默认返回 true，如果没有 pattern 属性
    }

    /**
     * 给表单元素添加错误样式
     * 该方法会将 'error' 类添加到指定的表单元素上，以表示该元素未通过验证
     * @param el - 要添加错误样式的表单元素
     */
    private addErrorStyles(el: HTMLElement): void {
        el.classList.add('error');
    }

    /**
     * 移除表单元素的错误样式
     * 该方法会将 'error' 类从指定的表单元素上移除，以表示该元素通过验证
     * @param el - 要移除错误样式的表单元素
     */
    private removeErrorStyles(el: HTMLElement): void {
        el.classList.remove('error');
    }

    /**
     * 绑定锚点点击事件
     */
    private bindAnchorEvents(): void {
        const anchorList = this.$el.querySelectorAll('.aimingPoint ul li');
        anchorList.forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.stopPropagation();
                const target = e.currentTarget as HTMLElement;
                this.scrollToTarget(target);
            });
        });
    }

    /**
     * 滚动到目标位置
     * @param anchorElement 锚点元素
     */
    protected scrollToTarget(anchorElement: HTMLElement): void {
        // 移除其他li的active类
        const allAnchors = this.$el.querySelectorAll('.aimingPoint ul li');
        allAnchors.forEach(a => a.classList.remove('active'));
        
        // 添加当前点击的li的active类
        anchorElement.classList.add('active');

        // 获取目标元素的id
        const targetId = anchorElement.getAttribute('data-target');
        if (!targetId) return;

        // 获取目标元素
        const targetElement = document.getElementById(targetId);
        if (!targetElement) return;

        // 使用 DOMUtils 滚动到目标位置
        DOMUtils.scrollToElement(targetElement, {
            behavior: 'smooth',
            block: 'start'
        });
    }
}
