/*
 * @Author: silverflute <EMAIL>
 * @Date: 2024-12-26 14:54:29
 * @LastEditors: silverflute <EMAIL>
 * @LastEditTime: 2024-12-30 15:04:08
 * @FilePath: /daorigin_wps_ts/src/types/financial.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export interface FinancialData {
    finance: Finance;
}

export interface Finance {
    buCode: string;//餐厅编号
    buName: string;//餐厅名称
    finMarket: string;//市场
    contractType: string;//合同类型
    strategicAlliance: string;//策略联盟业主
    bizType: string;//业务类别
    partyA: string;//甲方
    addOfPartyA: string;//甲方地址
    partyB: string;//合同乙方（我司市场法人）
    addOfPartyB: string;//乙方地址
    addOfStore: string;//租赁店铺地址
    signDate: string;//签约日期 YYYY-MM-dd
    openDate: string;//开业日期 YYYY-MM-dd
    estimateOpen: string;//下拉框 是否是预计开业日期？ 取值：预计、空 显示：是、否
    returnAreaYes: string;//租赁期满后是否恢复原状
    noSublet: string;//是否禁止转租
    areaSM: string;//计租面积(SM)
    areaRemark: string;//分楼层面积
    certEffectiveDate: string;//房产证生效日
    certExpiryDate: string;//房产证到期日
    leaseTypePlanning: string;//抽成租金为单一比例or多比例?
    leaseTypeDE: string;//租金类型
    leaseTypeRE: string;//RE/复核用
    depositAmount: string;//定金金额（元）
    idcAmount: string;//初始直接费用金额(元)
    leaseIncentives: string;//补偿金金额（元）
    transferSubject: string;//是否转入中转科目
    reNotes: string;//RE Notes
    bondAmount: string;//保证金金额（元）
    bondReturnDate: string;//返还时间
    guaranteeAmount: string;//保函金额（元）
    leaseDetails:LeaseDetail[];
}

export interface LeaseDetail {
    objectId: string;//唯一编号
    lease: Lease;
    rb: RecurringBilling;
    sov:SalesOverage;
}

export interface RecurringBilling {
    rbPaymentFrequency: string;//RB支付频率
    rbPaymentType: string;//RB支付类型
    paymentRent: string;//付款期间与租金所属期间关系
    dba: string;//DBA
    paymentEarlyRentYesDay: string;//提前/晚于月份数
    paymentLease: string;//付款方式
    rbFeeDesc: string;//其他费用具体描述
    rbAnnualSettleYes:string;//是否年度结算
    rentAnnual:string;//租赁年度定义
    rbtrRent:string;//固定租金税率（%）
    rbtrMgmt:string;//物管费税率（%）
    rbtrAdv:string;//"广告费税率（%）
    rbtrFee:string;//其他费用税率（%）
    relation:string;//关联要素
    phases:RbPhase[]
}

export interface RbPhase {
    rbId:string;//
    rbRateNum:number;//
    rbBeginDate:string;//开始日期 YY-MM-DD
    rbEndDate:string;//结束日期 YY-MM-DD
    rbAccruedAmountRent:string;//固定租金计提（元）
    rbAccruedAmountMgmt:string;//物管费计提（元）
    rbAccruedAmountAdv:string;//广告费计提（元）
    rbAccruedAmountFee:string;//其他费用计提（元）
    rbPaymentAmountRent:string;//固定租金付款（元）
    rbPaymentAmountMgmt:string;//物管费付款（元）
    rbPaymentAmountAdv:string;//广告费付款（元）
    rbPaymentAmountFee:string;//其他费用付款（元）
}

export interface SalesOverage {
    sovBreakMonthYes: string;//结算是否破月?
    sovLeaseVAT: string;//增值税税率%
    calculationMethod: string;//多档抽成计算方法
    sovPaymentType:string;//付款类型
    minMaxNeedYes:string;//是否需要计算固定与抽成孰高
    minMaxType1:string;//保底租金（和RENP取高）
    minMaxType2:string;//保底物管费（和MGMA取高）抽成租金（和RENP取高）
    sovRules:SovRule[];
}

export interface SovRule {
    sovId:string;//
    sovIndexNo:number;//
    sovAnnualSettleYes:string;//是否需要结算
    annualSettleFrequency:string;//结算频率
    annualSettleMonth:string;//结算月份
    sovBeginDate:string;//抽成开始日期 格式 YY-MM-DD
    sovEndDate:string;//抽成结束日期 格式 YY-MM-DD
    paymentOnSovYes:string;//是否要基于抽成付款
    sovRuleList:SovRuleList[];
}

export interface SovRuleList {
    sovRuleId:string;//
    rateNum:number;//
    breakpointYes:string;
    breakpoint:string;//突破点金额(元)
    sovPayPercentage:string;//付款抽成比例(%)
    sovAccPercentage:string;//计提抽成比例(%)
}

export interface Lease {
    leaseCode: string;//LeaseNo
    useOfLeaseNo: string;//LeaseNo.用途
    rentName: string;//租金名称
    rentType: string;//租赁类型
    landlordCode: string;//业主JDE号
    landlordName: string;//业主名称
    receivedCode: string;//实际收款人JDE号
    receivedName: string;//实际收款人名称
    taxplayerJde: string;//开票人JDE号(三流不合一时需要填写)
    taxplayerName: string;//开票人名称(三流不合一时需要填写)
    mergeOthersYes: string;//是否与同组内其他lease合并计算
    leaseGroup: string;//租赁组别
    strategyAlianceYes: string;//是否独立第三方物管费
    rentAreaYes: string;//是否归还面积
    yearlyNote: string;//年度通知单
    receiverNote: string;//通知单是否发给收款人
    monthlyNote: string;//月度通知单
    unit: string;//单元号
    beginDate: string;//房产交付日期
    freeBeginDate: string;//免租开始日期
    freeEndDate: string;//免租结束日期
    startRentDate: string;//起算日期
    orgEndDate: string;//租赁期限结束日期
    salesCalculation: string;//营业额取数规则
    relation: string;//关联要素
}