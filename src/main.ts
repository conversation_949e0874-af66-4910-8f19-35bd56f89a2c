//WebOffice
import WebOfficeSDKWrapper from "./weboffice/WebOfficeSDKWrapper.ts";
(<any>window).WebOfficeSDKWrapper=WebOfficeSDKWrapper;

//合同审核详情
import {ReviewDetail} from "./component/ReviewDetailsComponent.ts";
(<any>window).ReviewDetail=ReviewDetail;

//财务详情
// import { AlpineManager } from "./component/AlpineManager";
// import { FinancialSystemCom } from "./component/FinancialSystemCom";
// import { LeasesComponent } from "./component/LeasesComponent";
//
// // 将组件类和 AlpineManager 暴露给全局
// (<any>window).FinancialSystemCom = FinancialSystemCom;
// (<any>window).LeasesComponent = LeasesComponent;
// (<any>window).AlpineManager = AlpineManager; //页面最后执行 start 函数
