import WebOfficeSDK, { IWps } from '../lib/weboffice/web-office-sdk-solution';
import MD5 from 'crypto-js/md5';
import { EventEmitter } from 'eventemitter3';

type CommentsType = { author: string; content: string; date: number; len: number; pos: number }//评论的结构
type BookmarkType = { name: string, Range: { Start: number, End: number,length:number } }//书签的结构
//封装 wps 的类
export default class WebOfficeSDKWrapper {
    private instance: IWps;
    private app: any;
    private eventEmitter: EventEmitter;
    private _md5Ids: string[];

    /**
     * wps 初始化
     * @param appId   用用 id
     * @param fileId  文档 id
     * @param mount   容器 id
     * @param md5Ids  自定义 md5后的评论id组
     */
    constructor(appId: string, fileId: string, mount: string | HTMLElement, md5Ids: string[]) {
        this._md5Ids = md5Ids;
        this.instance = WebOfficeSDK.init({
            officeType: WebOfficeSDK.OfficeType.Writer,
            appId: appId,
            fileId: fileId,
            mount: mount
        });
        this.eventEmitter = new EventEmitter();
    }

    /**
     * 初始化
     */
    public async initialize() {
        try {
            await this.instance.ready();
            this.showLoadingMessage();
            this.app = this.instance.Application;
            this.app.ActiveDocument.ActiveWindow.View.ShowComments = false;
            const commentList = await this.app.ActiveDocument.GetComments();
            const bookmarks = this.transformData(commentList);
            await this.deleteCommentsInBatch(bookmarks);
            await this.addBookmarks(bookmarks);
            this.hideLoadingMessage();
        }catch (error) {
            this.hideLoadingMessage();
            console.error('初始化过程中发生错误：', error);
            this.eventEmitter.emit('error', { message: '初始化过程中发生错误', error });
        }
    }

    /**
     * 转换评论数据，为添加书签数据和删除特定的评论数据
     * @param dataArray
     * @private
     */
    private transformData(dataArray: CommentsType[]): BookmarkType[]{
        const bookmarks: BookmarkType[] = [];
        for (const data of dataArray) {
            const { author, content, date, pos, len } = data;
            const name = MD5(author + content + date).toString();

            if (!this._md5Ids.includes(name)) {
                const Range = { Start: pos, End: pos + len,length:len };
                bookmarks.push({ name, Range });
            }
        }
        return  bookmarks ;
    }


    /**
     * 批量删除评论
     * @param comments
     * @private
     */
    private async deleteCommentsInBatch(comments: BookmarkType[]): Promise<void> {
        const deleteTasks = comments.map(comment => this.deleteComment(comment.Range.Start, comment.Range.length));
        await Promise.all(deleteTasks);
    }

    /**
     * 删除指定位置的评论
     * @param start
     * @param length
     * @private
     */
    private async deleteComment(start: number, length: number): Promise<void> {
        try {
            await this.app.ActiveDocument.Comments.DeleteComment({ Start: start, Length: length });
        } catch (error) {
            console.error(`删除批注失败，错误信息：`, error);
            throw error; // 可以根据需要选择是否重新抛出错误
        }
    }


    /**
     * 批量添加书签
     * @param bookmarksList
     * @private
     */
    private async addBookmarks(bookmarksList: BookmarkType[]) {
        const bookmarks = await this.app.ActiveDocument.Bookmarks;
        const addTasks = bookmarksList.map(data => bookmarks.Add({ Name: data.name, Range: data.Range }));
        await Promise.all(addTasks);
    }

    /**
     * 发送显示loading 消息
     * @private
     */
    private showLoadingMessage() {
        this.eventEmitter.emit('loading', { message: 'Loading...' });
    }

    /**
     * 发送关闭loading 消息
     * @private
     */
    private hideLoadingMessage() {
        this.eventEmitter.emit('loadingCompleted', { message: 'Loading completed' });
    }

    /**
     * 添加监听
     * @param event
     * @param listener
     */
    public on(event: string, listener: (data: any) => void) {
        this.eventEmitter.on(event, listener);
    }

    /**
     * 光标选定的位置插入文本
     * @param text
     * @constructor
     */
    public async InsertAfter(text: string): Promise<void> {
        await this.app.ActiveDocument.ActiveWindow.Selection.InsertAfter(text);
    }

    /**
     * 删除书签
     * @param bookmarkName
     */
    public async deleteBookmark(bookmarkName: string) {
        await this.app.ActiveDocument.Bookmarks.Item(bookmarkName).Delete();
    }

    /**
     * 跳转到指定的书签的位置
     * @param bookmarkName
     */
    public async goToBookmark(bookmarkName: string): Promise<void> {
        const loadingTimeout = new Promise<void>((_, reject) => {
            const timeoutId = setTimeout(() => {
                this.showLoadingMessage();
                reject(new Error('Loading timeout'));
            }, 100);

            setTimeout(() => clearTimeout(timeoutId), 1000); // Clear the timeout if not triggered
        });

        const goToBookmarkPromise = new Promise<void>((resolve, reject) => {
            try {
                this.app.ActiveDocument.ActiveWindow.Selection.GoTo({
                    What: this.app.Enum.WdGoToItem.wdGoToBookmark,
                    Which: this.app.Enum.WdGoToDirection.wdGoToAbsolute,
                    Name: bookmarkName
                });
                resolve();
            } catch (error) {
                const errorMessage = `Failed to go to bookmark "${bookmarkName}": ${(error as Error).message}`;
                reject(new Error(errorMessage));
            }
        });

        try {
            await Promise.race([loadingTimeout, goToBookmarkPromise]);
        } catch (error) {
            if ((error as Error).message === 'Loading timeout') {
                try {
                    await goToBookmarkPromise;
                } finally {
                    this.hideLoadingMessage();
                }
            } else {
                throw error;
            }
        } finally {
            this.hideLoadingMessage();
        }
    }
}
